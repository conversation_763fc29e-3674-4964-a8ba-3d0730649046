-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_0_30
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MOREMOUNTAINS_NICEVIBRATIONS
-define:CROSS_PLATFORM_INPUT
-define:MOBILE_INPUT
-define:BCG_RCCP
-define:BCG_RCC
-define:UNITY_POST_PROCESSING_STACK_V2
-define:NWH_WC3D
-define:NWH_NVP2
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/BuildReportTool.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/AdvancedHelicopterController/Scripts/BulletScript.cs"
"Assets/AdvancedHelicopterController/Scripts/CollactableScript.cs"
"Assets/AdvancedHelicopterController/Scripts/EnemyAI.cs"
"Assets/AdvancedHelicopterController/Scripts/FollowTargetCamera.cs"
"Assets/AdvancedHelicopterController/Scripts/GameCanvas.cs"
"Assets/AdvancedHelicopterController/Scripts/Gasoline.cs"
"Assets/AdvancedHelicopterController/Scripts/GunController.cs"
"Assets/AdvancedHelicopterController/Scripts/GyroscopeViewController.cs"
"Assets/AdvancedHelicopterController/Scripts/HelicopterController.cs"
"Assets/AdvancedHelicopterController/Scripts/HelicopterSystemManager.cs"
"Assets/AdvancedHelicopterController/Scripts/HeliRotorController.cs"
"Assets/AdvancedHelicopterController/Scripts/MissileScript.cs"
"Assets/AdvancedHelicopterController/Scripts/RadarItem.cs"
"Assets/AdvancedHelicopterController/Scripts/RadarSystem.cs"
"Assets/AdvancedHelicopterController/Scripts/RadarTargetType.cs"
"Assets/AdvancedHelicopterController/Scripts/RadarTypeInfo.cs"
"Assets/AdvancedHelicopterController/Scripts/SimpleJoystick.cs"
"Assets/BoatControllerwithShooting/Scripts/BoatController.cs"
"Assets/BoatControllerwithShooting/Scripts/BoatEngineSound.cs"
"Assets/BoatControllerwithShooting/Scripts/BoatSystemManager.cs"
"Assets/BoatControllerwithShooting/Scripts/BulletScript.cs"
"Assets/BoatControllerwithShooting/Scripts/CollactableScript.cs"
"Assets/BoatControllerwithShooting/Scripts/EnemyAI.cs"
"Assets/BoatControllerwithShooting/Scripts/FollowTargetCamera.cs"
"Assets/BoatControllerwithShooting/Scripts/GameCanvas.cs"
"Assets/BoatControllerwithShooting/Scripts/Gasoline.cs"
"Assets/BoatControllerwithShooting/Scripts/GunController.cs"
"Assets/BoatControllerwithShooting/Scripts/GyroscopeViewController.cs"
"Assets/BoatControllerwithShooting/Scripts/MissileScript.cs"
"Assets/BoatControllerwithShooting/Scripts/NaturalAI.cs"
"Assets/BoatControllerwithShooting/Scripts/RadarItem.cs"
"Assets/BoatControllerwithShooting/Scripts/RadarSystem.cs"
"Assets/BoatControllerwithShooting/Scripts/RadarTargetType.cs"
"Assets/BoatControllerwithShooting/Scripts/RadarTypeInfo.cs"
"Assets/BoatControllerwithShooting/Scripts/SimpleJoystick.cs"
"Assets/BoatControllerwithShooting/Scripts/SpeedometerScript.cs"
"Assets/BoatControllerwithShooting/Scripts/WaterEffect.cs"
"Assets/BoatControllerwithShooting/Scripts/WaterSplashScript.cs"
"Assets/Game Script/Drive vehicle enter exit/allcanvasfalse.cs"
"Assets/Game Script/Drive vehicle enter exit/Drivevehicleenterexit.cs"
"Assets/Game Script/Drive vehicle enter exit/Dronecameraactivedeactive.cs"
"Assets/Game Script/Drive vehicle enter exit/OtherVehicleEnterexit.cs"
"Assets/Game Script/Drive vehicle enter exit/VehiclePlayerPosition.cs"
"Assets/Game Script/truckrcccam.cs"
"Assets/Game Script/Vehiclespawnsystem/VehiclespawnAroundPlayer.cs"
"Assets/Invector-3rdPersonController_LITE/3D Models/Others/HealthItem/vAnimateUV.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Camera/vThirdPersonCamera.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonAnimator.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonController.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonInput.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/CharacterController/vThirdPersonMotor.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vComment.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vExtensions.cs"
"Assets/Invector-3rdPersonController_LITE/Scripts/Generic/Utils/vPickupItem.cs"
"Assets/JMO Assets/Cartoon FX Remaster/CFXR Assets/Scripts/CFXR_Effect.CameraShake.cs"
"Assets/JMO Assets/Cartoon FX Remaster/CFXR Assets/Scripts/CFXR_Effect.cs"
"Assets/JMO Assets/Cartoon FX Remaster/CFXR Assets/Scripts/CFXR_EmissionBySurface.cs"
"Assets/JMO Assets/Cartoon FX Remaster/CFXR Assets/Scripts/CFXR_ParticleText.cs"
"Assets/JMO Assets/Cartoon FX Remaster/CFXR Assets/Scripts/CFXR_ParticleTextFontAsset.cs"
"Assets/JMO Assets/Cartoon FX Remaster/Demo Assets/CFXR_Demo.cs"
"Assets/JMO Assets/Cartoon FX Remaster/Demo Assets/CFXR_Demo_Rotate.cs"
"Assets/JMO Assets/Cartoon FX Remaster/Demo Assets/CFXR_Demo_Translate.cs"
"Assets/JMO Assets/Cartoon FX Remaster/Demo Assets/Kino Bloom/Bloom.cs"
"Assets/Joystick Pack/Examples/JoystickPlayerExample.cs"
"Assets/Joystick Pack/Examples/JoystickSetterExample.cs"
"Assets/Joystick Pack/Scripts/Base/Joystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/DynamicJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/FixedJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/FloatingJoystick.cs"
"Assets/Joystick Pack/Scripts/Joysticks/VariableJoystick.cs"
"Assets/MSK 2.2/Scripts/BikeAnimation.cs"
"Assets/MSK 2.2/Scripts/BikeCamera.cs"
"Assets/MSK 2.2/Scripts/BikeControl.cs"
"Assets/MSK 2.2/Scripts/BikeSwitch.cs"
"Assets/MSK 2.2/Scripts/BikeUIController.cs"
"Assets/MSK 2.2/Scripts/BikeUIManager.cs"
"Assets/MSK 2.2/Scripts/GizmoObject.cs"
"Assets/MSK 2.2/Scripts/PoliceLights.cs"
"Assets/MSK 2.2/Scripts/RearLookAt.cs"
"Assets/MSK 2.2/Skidmarks/Skidmarks Essentials/Skidmarks.cs"
"Assets/MSK 2.2/Skidmarks/WheelSkidmarks.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_CustomizationData.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_CustomizationDemo.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_CustomizationManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_CustomizationSetups.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_CustomizationTrigger.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customization_API.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Brake.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_CustomizationManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Decal.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_DecalManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Engine.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Handling.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Loadout.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Neon.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_NeonManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Paint.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_PaintManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Siren.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_SirenManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Speed.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_Spoiler.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_SpoilerManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_UpgradeManager.cs"
"Assets/RealisticCarControllerV4/Scripts/Customization/RCC_Customizer_WheelManager.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AIBrakeZone.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AIBrakeZonesContainer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AICarController.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AIO.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AIWaypointsContainer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_APIExample.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_AssetPaths.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_BehaviorTester.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Caliper.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Camera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CameraCarSelection.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CarControllerV4.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CarSelectionExample.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_ChangableWheels.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CharacterController.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CheckUp.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CinematicCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_ColorPickerBySliders.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Core.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CrashHammer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CrashPress.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_CrashShredder.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Damage.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DashboardColors.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DashboardInputs.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DashboardObjects.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Demo.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DemoMaterials.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DemoVehicles.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_DetachablePart.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Emission.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Exhaust.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_FixedCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_FOVForCinematicCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_FuelStation.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_GetBounds.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_GroundMaterials.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_HoodCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_InfoLabel.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_InitialSettings.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_InputActions.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_InputManager.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Inputs.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Installation.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Joint.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_LevelLoader.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Light.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_LOD.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Mirror.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_MobileButtons.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_MultipleBehaviorDemo.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Octree.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_OctreeNode.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_OverrideInputsExample.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_PlayerPrefsX.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_PoliceSiren.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Prop.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Recorder.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Records.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_RepairStation.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_SceneManager.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_SetLayer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Settings.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_ShadowRotConst.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_ShowroomCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Singleton.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Skidmarks.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_SkidmarksManager.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Spawner.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_SpeedLimiter.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_SuspensionArm.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Telemetry.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Teleporter.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_TrailerAttachPoint.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_TruckTrailer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_BehaviorButton.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_BehaviorSelector.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Canvas_Customizer.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Color.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Controller.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_CustomizationSlider.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_DashboardButton.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_DashboardDisplay.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Decal.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_DecalSetLocation.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Drag.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Joystick.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Neon.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_ShowroomCameraDrag.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Siren.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_SliderTextReader.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Spoiler.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_SteeringWheelController.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Upgrade.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_UI_Wheel.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Useless.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Version.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_Waypoint.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_WheelCamera.cs"
"Assets/RealisticCarControllerV4/Scripts/RCC_WheelCollider.cs"
"Assets/Script/AeroplaneEngine.cs"
"Assets/Script/Airplanecontroll.cs"
"Assets/Script/AirplaneCrah.cs"
"Assets/Script/CameraManager.cs"
"Assets/Script/WheelManager.cs"
"Assets/Scripts/Dronecamera.cs"
"Assets/Scripts/MobileUIController.cs"
"Assets/Scripts/UIButtonConnector.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/DecalDestroyer.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/ExtinguishableFire.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/GunAim.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/GunShoot.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/ParticleCollision.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/ParticleExamples.cs"
"Assets/UnityTechnologies/EffectExamples/Shared/Scripts/ParticleMenu.cs"
"Assets/UnityTechnologies/EffectExamples/TutorialInfo/Scripts/Readme.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/BakedSegment.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/Bezier.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/Convert.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/IGeneratedMesh.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/LinkedSplineFollower.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/OrientedPoint.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_Enums.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_GeneratedCollider.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_GeneratedMesh.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_IgnoredObject.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_MeshCapTag.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_MeshGenerationProfile.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_Theme.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SMR_UISettings.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/Spline.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SplineDefaultValues.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SplineFollower.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SplineMeshRenderer.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/SplinePrefabSpawner.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/UniqueMesh.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/WSM_TerrainBackup.cs"
"Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Scripts/WSM_TerrainTools.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/AnimationParameters.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/BellZone.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Camera/CameraFollow.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Camera/CameraSettings.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Camera/FlyingCamera.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/CustomEventZone.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/CustomWagonComponent.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/CustomWagonCreator.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/CustomWagonProfile.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/DemoUI_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/DirectionIndicator.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Extensions Interfaces/ILocomotive.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Extensions Interfaces/IRailwayVehicle.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Extensions Interfaces/ITrainCarCoupler.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Extensions Interfaces/ITrainDoorsController.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/GeneralSettings.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/HonkZone_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/PassengerTags.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Probability.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/RailroadSwitch_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/RailSensor.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/ReverseDirectionZone.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Route.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/RouteManager.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Sensors.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SFX.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SpawnPosition.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Speed.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SpeedChangeZone_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SplineBasedLocomotive.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SplineBasedTrainCarCoupler.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SplineBasedWagon.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/StationStopTrigger.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/SwitchTrigger.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TC_Enums.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainAttachPassenger.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainAudio.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainCarCoupler.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainController_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainDoor.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainDoorsController.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainInputSettings.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainParticles.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainPhysics.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainPlayerInput.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainProfile.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainSpawner.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainSpeedMonitor.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainStationController.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainSuspension.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainWheelsTruck.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/TrainWheel_v3.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/VFX.cs"
"Assets/WSM Game Studio/Train Controller_v3/Shared/Scripts/Wagon_v3.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"