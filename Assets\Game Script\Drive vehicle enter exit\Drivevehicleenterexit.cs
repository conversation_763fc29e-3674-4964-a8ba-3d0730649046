using UnityEngine;
using System.Collections;
using System.Reflection;

public class Drivevehicleenterexit : MonoBehaviour
{
    public GameObject Enterbutton;
    public GameObject[] Allvehicles;
    public GameObject Exitbutton;
    public float distance = 10f;
    public Transform Player;
    public GameObject Playercamera, vehiclecamera, Playercanvas, vehiclecanvas;
    private GameObject nearvehicle;
    private bool isInVehicle = false;
    public float maxExitSpeed = 5f; // Maximum speed to allow exit (in km/h)
    private Transform originalParent;

     // Store player's original parent
    public void Start()
    {
        // Find all vehicles with "Vehicle" and "Truck" tags and assign to Allvehicles array
        GameObject[] vehicles = GameObject.FindGameObjectsWithTag("Vehicle");

        // Combine both arrays into Allvehicles
        Allvehicles = new GameObject[vehicles.Length + GameObject.FindGameObjectsWithTag("Truck").Length];
        vehicles.CopyTo(Allvehicles, 0);
        GameObject.FindGameObjectsWithTag("Truck").CopyTo(Allvehicles, vehicles.Length);

        Enterbutton.SetActive(false);
        Exitbutton.SetActive(false);
    }

    public void Update()
    {
        if (!isInVehicle)
        {
            CheckNearVehicles();
        }
        else
        {
            CheckExitCondition();
        }
    }

    private void CheckNearVehicles()
    {
        bool nearVehicleFound = false;
        GameObject closestVehicle = null;
        float closestDistance = float.MaxValue;

        // Check distance to each vehicle in the Allvehicles array
        foreach (GameObject vehicle in Allvehicles)
        {
            if (vehicle != null && Player != null)
            {
                float distanceToVehicle = Vector3.Distance(Player.position, vehicle.transform.position);

                // If vehicle is within range and closer than previous closest
                if (distanceToVehicle <= distance && distanceToVehicle < closestDistance)
                {
                    nearVehicleFound = true;
                    closestVehicle = vehicle;
                    closestDistance = distanceToVehicle;
                }
            }
        }

        // Assign the closest vehicle as nearvehicle
        if (nearVehicleFound)
        {
            nearvehicle = closestVehicle;
        }
        else
        {
            nearvehicle = null; // Clear nearvehicle if no vehicle is near
        }

        // Show/hide enter button based on whether player is near a vehicle
        Enterbutton.SetActive(nearVehicleFound);
    }

    private void CheckExitCondition()
    {
        if (nearvehicle != null)
        {
            // Get vehicle controller component safely
            RCC_CarControllerV4 carController = nearvehicle.GetComponent<RCC_CarControllerV4>();
            if (carController != null && carController.enabled)
            {
                // Get vehicle speed in km/h
                float vehicleSpeed = carController.speed;

                // Show exit button only if speed is 5 km/h or less
                Exitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
            }
            else
            {
                // If controller is null or disabled, hide exit button
                Exitbutton.SetActive(false);
            }
        }
        else
        {
            // If no vehicle, hide exit button
            Exitbutton.SetActive(false);
        }
    }


    public void Entervehicle()
    {
        // Store original parent before parenting to vehicle
        originalParent = Player.parent;

        Playercamera.SetActive(false);
        Player.gameObject.SetActive(false);
        vehiclecamera.SetActive(true);
        Playercanvas.SetActive(false);
        vehiclecanvas.SetActive(true);
        nearvehicle.GetComponent<RCC_CarControllerV4>().canControl = true;

        // Parent player to vehicle and set position
        Player.SetParent(nearvehicle.transform);
        Player.position = nearvehicle.GetComponent<VehiclePlayerPosition>().SeatPosition.position;

        nearvehicle.GetComponent<RCC_CarControllerV4>().enabled = true;
        nearvehicle.GetComponent<RCC_CarControllerV4>().engineRunning = true;
        nearvehicle.GetComponent<Rigidbody>().linearDamping = 0.01f;
         nearvehicle.GetComponent<Rigidbody>().isKinematic = false;

        // Hide enter button and set vehicle state
        Enterbutton.SetActive(false);
        isInVehicle = true;
        // Exit button visibility will be controlled by CheckExitCondition based on speed
    }
    public void Exitvehicle()
    {
        if (nearvehicle == null) return; // Safety check

        // Store reference to vehicle controller before any operations
        RCC_CarControllerV4 carController = nearvehicle.GetComponent<RCC_CarControllerV4>();
        if (carController == null) return; // Safety check

        // Unparent player from vehicle first
        Player.SetParent(originalParent);

        Playercamera.SetActive(true);
        Player.gameObject.SetActive(true);
        vehiclecamera.SetActive(false);
        Playercanvas.SetActive(true);
        vehiclecanvas.SetActive(false);

        // Set player position to door position (now unparented, so it's relative to world)
        Player.position = nearvehicle.GetComponent<VehiclePlayerPosition>().DoorPosition.position;
        Player.rotation = nearvehicle.GetComponent<VehiclePlayerPosition>().DoorPosition.rotation;

        // Position camera behind the character when exiting vehicle
        StartCoroutine(PositionCameraBehindPlayer());

        // Immediately disable vehicle control and engine
        carController.canControl = false;
        carController.engineRunning = false;
        nearvehicle.GetComponent<Rigidbody>().linearDamping = 10f;

        // Start coroutine to safely disable the controller
        StartCoroutine(TurnOffEngineAfterDelay(carController));

        // Hide exit button and set vehicle state
        Exitbutton.SetActive(false);
        isInVehicle = false;
    }

    private IEnumerator TurnOffEngineAfterDelay(RCC_CarControllerV4 carController)
    {
        yield return new WaitForSeconds(0.5f); // Increased delay for safer operation

        // Double check that the controller still exists and is valid
        if (carController != null && carController.gameObject != null)
        {
            // Ensure the controller is fully stopped before disabling
            carController.canControl = false;
            carController.engineRunning = false;

            // Wait one more frame to ensure all operations are complete
            yield return null;

            // Finally disable the component
            carController.enabled = false;

            Debug.Log("Vehicle controller disabled successfully");
        }
        else
        {
            Debug.LogWarning("Car controller was null when trying to disable it");
        }
    }

    private IEnumerator PositionCameraBehindPlayer()
    {
        yield return new WaitForSeconds(0.1f); // Small delay to ensure camera is active

        if (Playercamera != null && Player != null)
        {
            // Try to get the vThirdPersonCamera component
            vThirdPersonCamera tpCamera = Playercamera.GetComponent<vThirdPersonCamera>();
            if (tpCamera != null)
            {
                // Use reflection to access private mouseX and mouseY fields for back camera positioning
                var mouseXField = typeof(vThirdPersonCamera).GetField("mouseX", BindingFlags.NonPublic | BindingFlags.Instance);
                var mouseYField = typeof(vThirdPersonCamera).GetField("mouseY", BindingFlags.NonPublic | BindingFlags.Instance);

                if (mouseXField != null && mouseYField != null)
                {
                    // Set camera behind the player (Y rotation = player's Y rotation)
                    mouseXField.SetValue(tpCamera, Player.eulerAngles.y);
                    mouseYField.SetValue(tpCamera, 0f); // Level camera (no up/down angle)
                }
            }
            else
            {
                // Fallback: Calculate position behind the player
                Vector3 behindPosition = Player.position - Player.forward * 3f + Vector3.up * 1.5f;
                Playercamera.transform.position = behindPosition;

                // Make camera look at the player
                Vector3 lookDirection = Player.position + Vector3.up * 1.5f - Playercamera.transform.position;
                if (lookDirection != Vector3.zero)
                {
                    Playercamera.transform.rotation = Quaternion.LookRotation(lookDirection);
                }
            }
        }
    }
}
