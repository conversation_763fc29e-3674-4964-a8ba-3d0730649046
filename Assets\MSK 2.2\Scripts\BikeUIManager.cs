using UnityEngine;

public class BikeUIManager : MonoBehaviour
{
    [<PERSON><PERSON>("UI Controller Reference")]
    public BikeUIController uiController;
    
    [<PERSON><PERSON>("Auto Setup")]
    public bool autoFindUIController = true;

    private void Start()
    {
        if (autoFindUIController && uiController == null)
        {
            uiController = FindObjectOfType<BikeUIController>();
            if (uiController == null)
            {
                Debug.Log<PERSON>arning("BikeUIController not found in scene! Please add one to use touch controls.");
            }
        }
    }

    // Call this method when player enters a bike
    public void SetActiveBike(BikeControl bike)
    {
        if (uiController != null)
        {
            uiController.SetActiveBike(bike);
            Debug.Log($"Active bike set to: {bike.name}");
        }
        else
        {
            Debug.LogWarning("BikeUIController not assigned!");
        }
    }

    // Call this method when player exits a bike
    public void ClearActiveBike()
    {
        if (uiController != null)
        {
            uiController.SetActiveBike(null);
            Debug.Log("Active bike cleared");
        }
    }

    // Method to get the UI controller reference
    public BikeUIController GetUIController()
    {
        return uiController;
    }

    // Method to manually assign UI controller
    public void SetUIController(BikeUIController controller)
    {
        uiController = controller;
    }
}
